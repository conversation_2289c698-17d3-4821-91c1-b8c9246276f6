[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "learning-machine"
version = "0.1.0"
description = "A portfolio of ML/AI applications built as part of a learning journey"
requires-python = ">=3.11"
dependencies = [
    "streamlit>=1.24.0",
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scikit-learn>=1.2.0",
    "matplotlib>=3.7.0",
    "tensorflow>=2.19.0",
    "umap-learn>=0.5.7",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.2.0",
    "isort>=6.0.1",
    "mypy>=1.15.0",
    "pytest>=8.3.5",
]

[tool.mypy]
python_version = "3.11"
strict = true
plugins = []
mypy_path = ["src"]
explicit_package_bases = true
ignore_missing_imports = true
show_error_codes = true
pretty = true
