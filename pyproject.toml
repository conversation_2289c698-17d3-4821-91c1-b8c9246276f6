[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "learning-machine"
version = "0.1.0"
description = "A portfolio of ML/AI applications built as part of a learning journey"
requires-python = ">=3.11"
dependencies = [
    "streamlit>=1.45.1",
    "pandas>=2.2.3",
    "numpy>=2.1.3",
    "scikit-learn>=1.6.1",
    "matplotlib>=3.10.3",
    "tensorflow>=2.19.0",
    "umap-learn>=0.5.7",
    "manim>=0.19.0",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "flake8>=7.2.0",
    "isort>=6.0.1",
    "mypy>=1.15.0",
    "pytest>=8.3.5",
]

[tool.mypy]
python_version = "3.11"
strict = true
plugins = []
mypy_path = ["src"]
explicit_package_bases = true
ignore_missing_imports = true
show_error_codes = true
pretty = true
