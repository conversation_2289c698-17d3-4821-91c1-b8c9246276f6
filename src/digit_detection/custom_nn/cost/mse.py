"""Mean Squared Error cost function implementation."""

import numpy as np
from numpy.typing import NDArray

from digit_detection.custom_nn.cost.cost import ICost


class MSECost(ICost):
    """Mean Squared Error cost function."""

    def forward(self, predictions: NDArray[np.float64], labels: NDArray[np.float64]) -> float:
        """Forward pass of MSE.

        Args:
            y_pred: Predicted values
            y_true: True values

        Returns:
            Mean squared error value
        """
        targets = self.build_targets(predictions, labels)
        return np.mean((predictions - targets) ** 2)

    def backward(self, predictions: NDArray[np.float64], labels: NDArray[np.float64]) -> NDArray[np.float64]:
        """Backward pass of MSE.

        Args:
            y_pred: Predicted values
            y_true: True values

        Returns:
            Gradient with respect to y_pred
        """
        targets = self.build_targets(predictions, labels)
        return 2 * (predictions - targets)
