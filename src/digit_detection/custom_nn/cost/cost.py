"""Cost function interface."""

from abc import ABC, abstractmethod
from numpy.typing import NDArray
import numpy as np


class ICost(ABC):
    """Interface for cost functions."""

    @abstractmethod
    def forward(
        self, 
        y_pred: NDArray[np.float64], 
        y_true: NDArray[np.float64]
    ) -> float:
        """Forward pass of the cost function.

        Args:
            y_pred: Predicted values
            y_true: True values

        Returns:
            Cost value
        """
        pass

    @abstractmethod
    def backward(
        self, 
        y_pred: NDArray[np.float64], 
        y_true: NDArray[np.float64]
    ) -> NDArray[np.float64]:

        """Backward pass of the cost function.

        Args:
            y_pred: Predicted values
            y_true: True values

        Returns:
            Gradient with respect to y_pred
        """
        pass

    def build_targets(
        self,
        predictions: NDArray[np.float64],
        labels: NDArray[np.float64]
    ) -> NDArray[np.float64]:

        count = predictions.shape[0]
        targets = np.zeros_like(predictions)
        targets[np.arange(count), labels] = 1

        return targets
