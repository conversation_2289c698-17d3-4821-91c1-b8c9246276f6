"""Activation function interface."""

from abc import ABC, abstractmethod
from numpy.typing import NDArray
import numpy as np


class IActivator(ABC):
    """Interface for activation functions."""

    @abstractmethod
    def forward(self, x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Forward pass of the activation function.

        Args:
            x: Input array

        Returns:
            Activated output
        """
        pass

    @abstractmethod
    def backward(self, x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Backward pass of the activation function.

        Args:
            x: Input array from the forward pass

        Returns:
            Gradient with respect to the input
        """
        pass
