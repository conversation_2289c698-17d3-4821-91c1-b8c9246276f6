"""Sigmoid activation function implementation."""

import numpy as np
from numpy.typing import NDArray

from digit_detection.custom_nn.activator.activator import IActivator


class SigmoidActivator(IActivator):
    """Sigmoid activation function."""

    def forward(self, x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Forward pass of Sigmoid.

        Args:
            x: Input array

        Returns:
            1 / (1 + exp(-x)) element-wise
        """
        # Clip to avoid overflow
        #x_clipped = np.clip(x, -500, 500)
        return 1 / (1 + np.exp(-x))

    def backward(self, x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Backward pass of Sigmoid.

        Args:
            x: Input array from the forward pass
            grad_output: Gradient from the next layer

        Returns:
            Gradient with respect to the input
        """
        # Compute sigmoid again
        sigmoid_x = self.forward(x)
        # Gradient of sigmoid is sigmoid(x) * (1 - sigmoid(x))
        return  sigmoid_x * (1 - sigmoid_x)
