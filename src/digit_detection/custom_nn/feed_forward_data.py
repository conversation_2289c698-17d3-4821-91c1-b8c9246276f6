"""Feed forward data container for neural network."""

from typing import List
from numpy.typing import NDArray
import numpy as np


class FeedForwardData:
    """Container for data generated during feed forward pass.
    
    This class stores the inputs and outputs for each layer
    during the forward pass of a neural network.
    
    Attributes:
        inputs: List of inputs to each layer (including input layer)
        outputs: List of outputs from each layer (including input layer)
    """
    
    def __init__(
            self,
            inputs: List[NDArray[np.float64]],
            outputs: List[NDArray[np.float64]]
        ):
        """Initialize feed forward data.
        
        Args:
            inputs: List of inputs to each layer
            outputs: List of outputs from each layer
        """
        self.inputs = inputs
        self.outputs = outputs
