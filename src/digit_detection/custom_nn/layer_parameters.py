"""Layer parameters data class for neural network configuration."""

from dataclasses import dataclass
from typing import Optional, List

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType


@dataclass
class LayerParameters:
    """Parameters for a neural network layer.

    Attributes:
        size: Number of neurons in the layer
        activator_type: Type of activator to use (None for input layer)
        is_input_layer: Whether this is an input layer (default: False)
    """
    size: int
    activator_type: Optional[ActivatorType] = None
    is_input_layer: bool = False

    def __post_init__(self) -> None:
        """Validate layer parameters after initialization."""
        if self.is_input_layer and self.activator_type is not None:
            raise ValueError("Input layer should not have an activator type")

        if not self.is_input_layer and self.activator_type is None:
            raise ValueError("Hidden and output layers must have an activator type")

class NetworkParameters:
    """Container for all parameters in a neural network.

    This class manages a collection of LayerParameters and provides
    convenience methods for working with the network structure.
    It also includes hyperparameters and cost function.
    """

    def __init__(
        self,
        layers: List[LayerParameters],
        cost_type: CostType,
        learning_rate: float = 0.01,
        batch_size: int = 32,
        epochs: int = 10,
    ):
        """Initialize network parameters.

        Args:
            layers: List of layer parameters for each layer in the network
            cost_type: Cost type to use for training
            learning_rate: Learning rate for optimization
            batch_size: Batch size for training
            epochs: Number of training epochs
        """
        # Ensure we have at least an input and output layer
        if len(layers) < 2:
            raise ValueError("Network must have at least an input and output layer")

        # Ensure the first layer is marked as input layer
        if not layers[0].is_input_layer:
            raise ValueError("First layer must be an input layer")

        # Ensure no other layers are marked as input layers
        if any(layer.is_input_layer for layer in layers[1:]):
            raise ValueError("Only the first layer can be an input layer")

        self.layers = layers
        self.cost_type = cost_type
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs

    def pretty_print(self) -> str:
        """Return a formatted string representation of the network parameters.

        Returns:
            Formatted string with network parameters
        """
        result = []
        result.append("=== Network Parameters ===")
        result.append(f"Number of layers: {len(self.layers)}")

        # Layer details
        result.append("Layer structure:")
        for i, layer in enumerate(self.layers):
            layer_type = "Input" if layer.is_input_layer else "Hidden" if i < len(self.layers) - 1 else "Output"
            activator = "None" if layer.activator_type is None else layer.activator_type.name
            result.append(f"  Layer {i} ({layer_type}): Size={layer.size}, Activator={activator}")

        # Hyperparameters
        result.append(f"Cost type: {self.cost_type.name}")
        result.append(f"Learning rate: {self.learning_rate}")
        result.append(f"Batch size: {self.batch_size}")
        result.append(f"Epochs: {self.epochs}")
        result.append("=========================")

        return "\n".join(result)

    def __str__(self) -> str:
        """Return a string representation of the network parameters.

        Returns:
            String representation
        """
        return self.pretty_print()
