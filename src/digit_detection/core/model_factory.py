"""Core model interfaces and abstract classes."""

from typing import Dict, Type


from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.model import Model, ModelConfig


class ModelFactory:
    """Factory for creating model instances."""

    _models: Dict[FlavourType, Type[Model]] = {}

    @classmethod
    def register(cls, flavor: FlavourType, model_class: Type[Model]) -> None:
        """Register a model class for a specific flavor.

        Args:
            flavor: Model flavor/backend
            model_class: Model class to register
        """
        cls._models[flavor] = model_class

    @classmethod
    def create_model(cls, config: ModelConfig) -> Model:
        """Create a model instance for the given configuration.

        Args:
            config: Model configuration

        Returns:
            Model instance

        Raises:
            ValueError: If the model flavor is not supported
        """
        if config.flavor not in cls._models:
            raise ValueError(f"Unsupported model flavor: {config.flavor}")

        model_class = cls._models[config.flavor]
        return model_class(config)