"""Core model interfaces and abstract classes."""

from abc import ABC, abstractmethod
from digit_detection.custom_nn.epoch_data import EpochData
from numpy.typing import NDArray
from typing import Any, Dict, List, Optional, Type

import numpy as np

from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.core.dataset_type import DatasetType


class ModelMetrics:
    """Metrics for model evaluation."""

    def __init__(
        self,
        accuracy: float,
        loss: float,
        confusion_matrix: Optional[NDArray[np.float64]] = None,
        additional_metrics: Optional[Dict[str, float]] = None,
    ) -> None:
        """Initialize model metrics.

        Args:
            accuracy: Model accuracy (0-1)
            loss: Model loss value
            confusion_matrix: Confusion matrix for classification
            additional_metrics: Additional model-specific metrics
        """
        self.accuracy = accuracy
        self.loss = loss
        self.confusion_matrix = confusion_matrix
        self.additional_metrics = additional_metrics or {}


class ModelConfig:
    """Configuration for model creation."""

    def __init__(
        self,
        layer_sizes: List[int],
        activator_types: List[ActivatorType],
        cost_type: CostType,
        learning_rate: float = 0.01,
        batch_size: int = 32,
        epochs: int = 10,
        flavor: FlavourType = FlavourType.CUSTOM,
        random_seed: int = 42,
    ) -> None:
        """Initialize model configuration.

        Args:
            layer_sizes: List of layer sizes (including input and output layers)
            activator_types: List of activation functions for each layer (excluding input layer)
            cost_type: Cost function type
            learning_rate: Learning rate for optimization
            batch_size: Batch size for training
            epochs: Number of training epochs
            flavor: Model flavor/backend
            random_seed: Random seed for reproducibility
        """
        # Validate layer sizes and activation functions
        if len(layer_sizes) < 2:
            raise ValueError("Model must have at least input and output layers")

        if len(activator_types) != len(layer_sizes) - 1:
            raise ValueError(
                f"Number of activation functions ({len(activator_types)}) must match "
                f"number of layers excluding input layer ({len(layer_sizes) - 1})"
            )

        self.layer_sizes = layer_sizes
        self.activator_types = activator_types
        self.cost_type = cost_type
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.epochs = epochs
        self.flavor = flavor
        self.random_seed = random_seed

    def pretty_print(self) -> str:
        """Return a formatted string representation of the model configuration.

        Returns:
            Formatted string with model configuration
        """
        result = []
        result.append("=== Model Configuration ===")

        # Architecture
        result.append("Architecture:")
        result.append(f"  Input layer size: {self.layer_sizes[0]}")

        for i in range(1, len(self.layer_sizes) - 1):
            result.append(f"  Hidden layer {i} size: {self.layer_sizes[i]}, Activation: {self.activator_types[i-1].name}")

        result.append(f"  Output layer size: {self.layer_sizes[-1]}, Activation: {self.activator_types[-1].name}")

        # Training parameters
        result.append("Training parameters:")
        result.append(f"  Cost function: {self.cost_type.name}")
        result.append(f"  Learning rate: {self.learning_rate}")
        result.append(f"  Batch size: {self.batch_size}")
        result.append(f"  Epochs: {self.epochs}")

        # Other parameters
        result.append("Other parameters:")
        result.append(f"  Model flavor: {self.flavor.name}")
        result.append(f"  Random seed: {self.random_seed}")
        result.append("===========================")

        return "\n".join(result)

    def __str__(self) -> str:
        """Return a string representation of the model configuration.

        Returns:
            String representation
        """
        return self.pretty_print()


class Model(ABC):
    """Interface for model implementations."""

    @abstractmethod
    def __init__(
        self,
        model_config: ModelConfig
    ) -> None:
        """Initialize model.

        Args:
            model_config: Model configuration
        """
        pass
        
    @abstractmethod
    def train(
        self,
        x_train: NDArray[np.float64],
        y_train: NDArray[np.float64],
        x_val: Optional[NDArray[np.float64]] = None,
        y_val: Optional[NDArray[np.float64]] = None,
    ) -> List[EpochData]:
        """Train the model on the provided data.

        Args:
            x_train: Training features
            y_train: Training labels
            x_val: Validation features
            y_val: Validation labels

        Returns:
            Dictionary of training history (loss, accuracy, etc.)
        """
        pass

    @abstractmethod
    def predict(self, x: NDArray[np.float64]) -> NDArray[np.float64]:
        """Make predictions on new data.

        Args:
            x: Input features

        Returns:
            Predicted values
        """
        pass

    @abstractmethod
    def evaluate(self, x_test: NDArray[np.float64], y_test: NDArray[np.float64]) -> ModelMetrics:
        """Evaluate the model on test data.

        Args:
            x_test: Test features
            y_test: Test labels

        Returns:
            Model metrics
        """
        pass

    @abstractmethod
    def save(self, path: str) -> None:
        """Save the model to disk.

        Args:
            path: Path to save the model
        """
        pass

    @classmethod
    @abstractmethod
    def load(cls, path: str) -> "Model":
        """Load the model from disk.

        Args:
            path: Path to load the model from

        Returns:
            Loaded model
        """
        pass





class IDataLoader(ABC):
    """Interface for data loaders."""

    @abstractmethod
    def load_data(self) -> "DataBundle":
        """Load the data.

        Returns:
            DataBundle containing training, validation, and test data
        """
        pass


class DataBundle:
    """Container for dataset splits."""

    def __init__(
        self,
        x_train: NDArray[np.float64],
        y_train: NDArray[np.float64],
        x_test: NDArray[np.float64],
        y_test: NDArray[np.float64],
        x_val: Optional[NDArray[np.float64]] = None,
        y_val: Optional[NDArray[np.float64]] = None,
    ) -> None:
        """Initialize data bundle.

        Args:
            x_train: Training features
            y_train: Training labels
            x_test: Test features
            y_test: Test labels
            x_val: Validation features
            y_val: Validation labels
        """
        self.x_train = x_train
        self.y_train = y_train
        self.x_test = x_test
        self.y_test = y_test
        self.x_val = x_val
        self.y_val = y_val

    def pretty_print(self) -> str:
        """Return a formatted string representation of the data bundle.

        Returns:
            Formatted string with data bundle information
        """
        result = []
        result.append("=== Data Bundle Information ===")

        # Training data
        result.append(f"Training data:")
        result.append(f"  X_train shape: {self.x_train.shape}")
        result.append(f"  y_train shape: {self.y_train.shape}")

        # Test data
        result.append(f"Test data:")
        result.append(f"  X_test shape: {self.x_test.shape}")
        result.append(f"  y_test shape: {self.y_test.shape}")

        # Validation data (if available)
        if self.x_val is not None and self.y_val is not None:
            result.append(f"Validation data:")
            result.append(f"  X_val shape: {self.x_val.shape}")
            result.append(f"  y_val shape: {self.y_val.shape}")
        else:
            result.append("Validation data: None")

        # Data statistics
        result.append(f"Data statistics:")
        result.append(f"  Number of training samples: {self.x_train.shape[0]}")
        result.append(f"  Number of test samples: {self.x_test.shape[0]}")
        if self.x_val is not None:
            result.append(f"  Number of validation samples: {self.x_val.shape[0]}")
        result.append(f"  Input feature dimension: {self.x_train.shape[1]}")
        result.append(f"  Output dimension: {self.y_train.shape[1] if len(self.y_train.shape) > 1 else 1}")
        result.append("==============================")

        return "\n".join(result)

    def __str__(self) -> str:
        """Return a string representation of the data bundle.

        Returns:
            String representation
        """
        return self.pretty_print()


class DataLoaderFactory:
    """Factory for creating data loader instances."""

    _data_loaders: Dict[DatasetType, Type[IDataLoader]] = {}

    @classmethod
    def register(cls, dataset_type: DatasetType, data_loader_class: Type[IDataLoader]) -> None:
        """Register a data loader class for a specific dataset type.

        Args:
            dataset_type: Dataset type
            data_loader_class: Data loader class to register
        """
        cls._data_loaders[dataset_type] = data_loader_class

    @classmethod
    def create_data_loader(cls, dataset_type: DatasetType) -> IDataLoader:
        """Create a data loader instance for the given dataset type.

        Args:
            dataset_type: Dataset type

        Returns:
            Data loader instance

        Raises:
            ValueError: If the dataset type is not supported
        """
        if dataset_type not in cls._data_loaders:
            raise ValueError(f"Unsupported dataset type: {dataset_type}")

        data_loader_class = cls._data_loaders[dataset_type]
        return data_loader_class()