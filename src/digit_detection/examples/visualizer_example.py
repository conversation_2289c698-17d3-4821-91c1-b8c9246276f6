"""Example demonstrating the use of the Visualizer class."""

import numpy as np
import matplotlib.pyplot as plt
from typing import List

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.core.dataset_type import DatasetType
from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.model import ModelConfig, DataLoaderFactory
from digit_detection.core.model_factory import ModelFactory
from digit_detection.core.projector import Projector, ProjectionMethod, ProjectionTarget, ProjectionDimension
from digit_detection.custom_nn.neural_network import NeuralNetwork
from digit_detection.custom_nn.epoch_data import EpochData
from digit_detection.adapters.mnist_data_loader import M<PERSON><PERSON><PERSON><PERSON>Loader


def main() -> None:
    """Run the visualizer example."""
    # Register models and data loaders
    ModelFactory.register(FlavourType.CUSTOM, NeuralNetwork)
    DataLoaderFactory.register(DatasetType.MNIST, MNISTDataLoader)

    # Load data
    data_loader = DataLoaderFactory.create_data_loader(DatasetType.MNIST)
    data_bundle = data_loader.load_data()

    # Create a small subset for quick demonstration
    subset_size = 1000
    indices = np.random.choice(data_bundle.x_test.shape[0], subset_size, replace=False)
    x_test_subset = data_bundle.x_test[indices]
    y_test_subset = data_bundle.y_test[indices]

    # Normalize data
    x_test_subset = x_test_subset / 255.0

    # Create model configuration
    model_config = ModelConfig(
        layer_sizes=[784, 128, 64, 10],
        activator_types=[ActivatorType.SIGMOID, ActivatorType.SIGMOID, ActivatorType.SIGMOID],
        cost_type=CostType.MEAN_SQUARED_ERROR,
        learning_rate=0.1,
        batch_size=32,
        epochs=5,
        flavor=FlavourType.CUSTOM,
        random_seed=42
    )

    # Create model
    model = ModelFactory.create_model(model_config)

    # Train model (just a few epochs for demonstration)
    print("Training model...")
    epoch_data = model.train(
        x_train=data_bundle.x_train[:5000] / 255.0,  # Use a small subset for quick training
        y_train=data_bundle.y_train[:5000],
        x_test=x_test_subset,
        y_test=y_test_subset
    )

    # Create visualizer
    visualizer = Projector(
        x_raw=x_test_subset,
        y_true=y_test_subset,
        model=model
    )

    # Create projections using different methods
    print("\nCreating projections...")

    # PCA projection (2D)
    pca_data = visualizer.create_projection(
        epoch=len(epoch_data) - 1,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.PCA,
        dimension=ProjectionDimension.TWO_D
    )

    # t-SNE projection (2D)
    tsne_data = visualizer.create_projection(
        epoch=len(epoch_data) - 1,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.TSNE,
        dimension=ProjectionDimension.TWO_D
    )

    # UMAP projection (2D)
    umap_data = visualizer.create_projection(
        epoch=len(epoch_data) - 1,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.UMAP,
        dimension=ProjectionDimension.TWO_D
    )

    # PCA projection (3D)
    pca_3d_data = visualizer.create_projection(
        epoch=len(epoch_data) - 1,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.PCA,
        dimension=ProjectionDimension.THREE_D
    )

    # Visualize projections
    print("Visualizing projections...")

    # 2D projections
    _ = visualizer.visualize_projection(
        pca_data,
        title="PCA Projection (2D)",
        dimension=ProjectionDimension.TWO_D
    )

    _ = visualizer.visualize_projection(
        tsne_data,
        title="t-SNE Projection (2D)",
        dimension=ProjectionDimension.TWO_D
    )

    _ = visualizer.visualize_projection(
        umap_data,
        title="UMAP Projection (2D)",
        dimension=ProjectionDimension.TWO_D
    )

    # 3D projection
    _ = visualizer.visualize_projection(
        pca_3d_data,
        title="PCA Projection (3D)",
        dimension=ProjectionDimension.THREE_D
    )

    # Show figures
    plt.show()

    # Alternative: create visualizer from epoch data
    print("\nCreating visualizer from epoch data...")
    visualizer_from_epoch = Projector.from_epoch_data(
        epoch_data=epoch_data,
        model=model
    )

    # Create and visualize projection using string parameters
    projection_data = visualizer_from_epoch.create_projection(
        epoch=len(epoch_data) - 1,
        target="INPUT",      # String version also works
        method="PCA",        # String version also works
        dimension="TWO_D"    # String version also works
    )

    _ = visualizer_from_epoch.visualize_projection(
        projection_data,
        title="Projection from Epoch Data",
        dimension="TWO_D"    # String version also works
    )
    plt.show()


if __name__ == "__main__":
    main()
