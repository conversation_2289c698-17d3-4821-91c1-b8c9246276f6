"""Example demonstrating the use of CustomModel adapter with the custom neural network."""

import numpy as np
from sklearn.datasets import load_digits
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder

from digit_detection.core.model import ModelConfig, ModelFactory
from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType


def main() -> None:
    """Run the example."""
    print("Loading MNIST digits dataset...")
    # Load digits dataset
    digits = load_digits()
    X = digits.data
    y = digits.target

    # Preprocess data
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # One-hot encode labels
    encoder = OneHotEncoder(sparse_output=False)
    y_onehot = encoder.fit_transform(y.reshape(-1, 1))

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y_onehot, test_size=0.2, random_state=42
    )

    print(f"Data shapes: X_train: {X_train.shape}, y_train: {y_train.shape}, X_test: {X_test.shape}, y_test: {y_test.shape}")

    # Create model configuration
    print("\nCreating model configuration...")
    config = ModelConfig(
        layer_sizes=[64, 32, 10],  # Input layer (64 features) + hidden layer + output layer (10 digits)
        activator_types=[
            ActivatorType.SIGMOID,  # Hidden layer
            ActivatorType.SIGMOID   # Output layer
        ],
        cost_type=CostType.MEAN_SQUARED_ERROR,
        learning_rate=0.1,
        batch_size=32,
        epochs=10,
        flavor=FlavourType.CUSTOM,
        random_seed=42
    )

    # Create model
    print("Creating and building model...")
    model = ModelFactory.create_model(config)
    model.build(config)

    # Train model
    print("\nTraining model...")
    history = model.train(X_train, y_train)

    # Evaluate model
    print("\nEvaluating model...")
    metrics = model.evaluate(X_test, y_test)

    print(f"\nTest accuracy: {metrics.accuracy:.4f}")
    print(f"Test loss: {metrics.loss:.4f}")

    # Make predictions
    print("\nMaking predictions on a few examples...")
    predictions = model.predict(X_test[:5])
    pred_classes = np.argmax(predictions, axis=1)
    true_classes = np.argmax(y_test[:5], axis=1)

    for i in range(5):
        print(f"Example {i+1}: Predicted: {pred_classes[i]}, Actual: {true_classes[i]}")

    # Save model
    print("\nSaving model...")
    model.save("models/custom_model.pkl")

    print("Done!")


if __name__ == "__main__":
    main()
