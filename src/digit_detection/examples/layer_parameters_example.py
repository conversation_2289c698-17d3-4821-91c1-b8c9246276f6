"""Example demonstrating the use of LayerParameters."""

# No need for numpy in this example

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.custom_nn.layer_parameters import LayerParameters, NetworkParameters, LayerSpec
from digit_detection.custom_nn.neural_network import create_network, create_network_from_specs, NeuralNetwork


def main() -> None:
    """Run the example."""
    # Method 1: Create network using layer_sizes and activator_types lists
    print("Method 1: Using layer_sizes and activator_types lists")
    layer_sizes = [784, 128, 64, 10]  # Input layer (784) + hidden layers + output layer (10)
    activator_types = [
        ActivatorType.SIGMOID,   # First hidden layer
        ActivatorType.SIGMOID,   # Second hidden layer
        ActivatorType.SIGMOID    # Output layer
    ]

    # Create network
    network1 = create_network(
        layer_sizes=layer_sizes,
        activator_types=activator_types,
        cost_function=CostType.MEAN_SQUARED_ERROR,
        learning_rate=0.01,
        batch_size=32,
        epochs=10
    )

    print(f"Network created with {len(network1.network_parameters)} layers")
    print(f"Layer sizes: {network1.network_parameters.layer_sizes}")
    print(f"Number of weights: {sum(w.size for w in network1.weights)}")
    print()

    # Method 2: Create network using explicit LayerParameters
    print("Method 2: Using explicit LayerParameters")
    layers = [
        # Input layer (no activation function)
        LayerParameters(size=784, is_input_layer=True),

        # Hidden layers
        LayerParameters(size=128, activator_type=ActivatorType.SIGMOID),
        LayerParameters(size=64, activator_type=ActivatorType.SIGMOID),

        # Output layer
        LayerParameters(size=10, activator_type=ActivatorType.SIGMOID)
    ]

    # Create NetworkParameters
    network_params = NetworkParameters(
        layers=layers,
        cost_function=CostType.MEAN_SQUARED_ERROR,
        learning_rate=0.01,
        batch_size=32,
        epochs=10
    )

    # Create network directly from NetworkParameters
    network2 = NeuralNetwork(network_parameters=network_params)

    print(f"Network created with {len(network2.network_parameters)} layers")
    print(f"Layer sizes: {network2.network_parameters.layer_sizes}")
    print(f"Number of weights: {sum(w.size for w in network2.weights)}")
    print()

    # Method 3: Using LayerSpec (most readable approach)
    print("Method 3: Using LayerSpec (most readable approach)")
    layer_specs = [
        # Input layer (no activation function)
        LayerSpec(size=784),

        # Hidden layers
        LayerSpec(size=128, activation=ActivatorType.SIGMOID),
        LayerSpec(size=64, activation=ActivatorType.SIGMOID),

        # Output layer
        LayerSpec(size=10, activation=ActivatorType.SIGMOID)
    ]

    # Create network directly from specs
    network3 = create_network_from_specs(
        layer_specs=layer_specs,
        cost_function=CostType.MEAN_SQUARED_ERROR,
        learning_rate=0.01,
        batch_size=32,
        epochs=10
    )

    print(f"Network created with {len(network3.network_parameters)} layers")
    print(f"Layer sizes: {network3.network_parameters.layer_sizes}")
    print(f"Number of weights: {sum(w.size for w in network3.weights)}")

    # Demonstrate validation
    print("\nValidation examples:")
    try:
        # Try to create an input layer with activator type (should fail)
        # Using _ to indicate we don't use the variable (suppresses linter warnings)
        _ = LayerParameters(size=784, activator_type=ActivatorType.SIGMOID, is_input_layer=True)
        print("Created invalid input layer with activator type (should have failed)")
    except ValueError as e:
        print(f"Correctly caught error: {e}")

    try:
        # Try to create a hidden layer without activator type (should fail)
        # Using _ to indicate we don't use the variable (suppresses linter warnings)
        _ = LayerParameters(size=128)
        print("Created invalid hidden layer without activator type (should have failed)")
    except ValueError as e:
        print(f"Correctly caught error: {e}")

    try:
        # Try to create invalid layer specs (input layer with activation)
        invalid_specs = [
            LayerSpec(size=784, activation=ActivatorType.SIGMOID),  # Input layer shouldn't have activation
            LayerSpec(size=128, activation=ActivatorType.SIGMOID),
            LayerSpec(size=10, activation=ActivatorType.SIGMOID)
        ]
        NetworkParameters.from_layer_specs(
            specs=invalid_specs,
            cost_function=CostType.MEAN_SQUARED_ERROR
        )
        print("Created invalid layer specs (should have failed)")
    except ValueError as e:
        print(f"Correctly caught error: {e}")


if __name__ == "__main__":
    main()
