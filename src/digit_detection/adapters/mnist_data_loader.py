"""MNIST dataset loader implementation."""

import numpy as np
from tensorflow.keras.datasets import mnist

from digit_detection.core.model import IData<PERSON>oa<PERSON>, DataBundle


class MNISTDataLoader(IDataLoader):
    """Data loader for MNIST dataset."""

    def load_data(self) -> DataBundle:
        """Load MNIST dataset.

        Returns:
            DataBundle containing training, validation, and test data
        """

        (x_train, y_train), (x_test, y_test) = mnist.load_data()
        x_train = x_train.reshape(-1, 28*28)
        x_test = x_test.reshape(-1, 28*28)

        # Create data bundle
        data_bundle = DataBundle(
            x_train=x_train,
            y_train=y_train,
            x_test=x_test,
            y_test=y_test
        )

        # Print data bundle information
        print(f"\n{data_bundle.pretty_print()}\n")

        return data_bundle