"""Custom dataset loader implementation."""

import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder

from digit_detection.core.model import IDataLoader, DataBundle


class CustomDataLoader(IDataLoader):
    """Data loader for custom dataset."""

    def load_data(self) -> DataBundle:
        """Load custom dataset.

        Returns:
            DataBundle containing training, validation, and test data
        """
        # Generate random data for demonstration purposes
        # In a real application, this would load data from a file or database
        num_samples = 1000
        num_features = 784  # 28x28 images
        num_classes = 10    # 10 digits (0-9)

        # Generate random features
        X = np.random.randn(num_samples, num_features)

        # Generate random labels (integers from 0 to 9)
        y_raw = np.random.randint(0, num_classes, size=num_samples)

        # Preprocess data
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # One-hot encode labels
        encoder = OneHotEncoder(sparse_output=False)
        y_onehot = encoder.fit_transform(y_raw.reshape(-1, 1))

        # Split data into train, validation, and test sets
        X_train, X_temp, y_train, y_temp = train_test_split(
            X_scaled, y_onehot, test_size=0.3, random_state=42
        )

        X_val, X_test, y_val, y_test = train_test_split(
            X_temp, y_temp, test_size=0.5, random_state=42
        )

        # Create data bundle
        data_bundle = DataBundle(
            x_train=X_train,
            y_train=y_train,
            x_test=X_test,
            y_test=y_test,
            x_val=X_val,
            y_val=y_val
        )

        # Print data bundle information
        print(f"\n{data_bundle.pretty_print()}\n")

        return data_bundle
